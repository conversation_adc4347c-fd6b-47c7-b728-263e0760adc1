/**
 * @fileoverview JSDoc annotation parsing with comment-parser
 * @implements milestone-M1.2#AnnotationParser
 */

import type { Annotation } from './types.js';

/**
 * Parse @implements annotations from source code
 * @param fileContent - Source code content
 * @param filePath - Path to the source file
 * @returns Array of parsed annotations
 */
export function parseAnnotations(
  _fileContent: string,
  _filePath: string
): Annotation[] {
  // TODO: Implement in Task 04 - annotation-parser
  throw new Error('parseAnnotations not yet implemented - Task 04');
}
